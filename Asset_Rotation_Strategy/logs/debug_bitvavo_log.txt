025-07-27 00:03:07,201 - [BITVA<PERSON>] - root - INFO - Saved allocation history to allocation_history_weighted_50-50_1d_1d_with_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-07-27 00:03:07,202 - [BITVAVO] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-27 00:03:07,202 - [BITVAVO] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-27 00:03:07,202 - [BITVAVO] - root - INFO -   ETH/USDT: First available date 2024-10-13
2025-07-27 00:03:07,202 - [BITVAVO] - root - INFO -   BTC/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   SOL/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   SUI/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVA<PERSON>] - root - INFO -   XRP/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   AAVE/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   AVAX/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   ADA/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   LINK/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   TRX/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   PEPE/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   DOGE/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   BNB/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO -   DOT/USDT: First available date 2024-10-13
2025-07-27 00:03:07,203 - [BITVAVO] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-27 00:03:07,207 - [BITVAVO] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,209 - [BITVAVO] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,211 - [BITVAVO] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,213 - [BITVAVO] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,215 - [BITVAVO] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,217 - [BITVAVO] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,219 - [BITVAVO] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,221 - [BITVAVO] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,223 - [BITVAVO] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,225 - [BITVAVO] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,227 - [BITVAVO] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,229 - [BITVAVO] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,231 - [BITVAVO] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,233 - [BITVAVO] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-27 00:03:07,236 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 287 points
2025-07-27 00:03:07,236 - [BITVAVO] - root - INFO - ETH/USDT B&H total return: 40.58%
2025-07-27 00:03:07,239 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 287 points
2025-07-27 00:03:07,240 - [BITVAVO] - root - INFO - BTC/USDT B&H total return: 21.03%
2025-07-27 00:03:07,243 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 287 points
2025-07-27 00:03:07,243 - [BITVAVO] - root - INFO - SOL/USDT B&H total return: -7.78%
2025-07-27 00:03:07,246 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 287 points
2025-07-27 00:03:07,247 - [BITVAVO] - root - INFO - SUI/USDT B&H total return: 29.34%
2025-07-27 00:03:07,250 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 287 points
2025-07-27 00:03:07,250 - [BITVAVO] - root - INFO - XRP/USDT B&H total return: 30.63%
2025-07-27 00:03:07,253 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 287 points
2025-07-27 00:03:07,254 - [BITVAVO] - root - INFO - AAVE/USDT B&H total return: 16.75%
2025-07-27 00:03:07,257 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 287 points
2025-07-27 00:03:07,258 - [BITVAVO] - root - INFO - AVAX/USDT B&H total return: -2.84%
2025-07-27 00:03:07,261 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 287 points
2025-07-27 00:03:07,261 - [BITVAVO] - root - INFO - ADA/USDT B&H total return: 15.12%
2025-07-27 00:03:07,264 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 287 points
2025-07-27 00:03:07,264 - [BITVAVO] - root - INFO - LINK/USDT B&H total return: -2.13%
2025-07-27 00:03:07,267 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 287 points
2025-07-27 00:03:07,268 - [BITVAVO] - root - INFO - TRX/USDT B&H total return: 30.02%
2025-07-27 00:03:07,270 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 287 points
2025-07-27 00:03:07,271 - [BITVAVO] - root - INFO - PEPE/USDT B&H total return: 29.30%
2025-07-27 00:03:07,274 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 287 points
2025-07-27 00:03:07,274 - [BITVAVO] - root - INFO - DOGE/USDT B&H total return: -7.83%
2025-07-27 00:03:07,277 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 287 points
2025-07-27 00:03:07,277 - [BITVAVO] - root - INFO - BNB/USDT B&H total return: 28.23%
2025-07-27 00:03:07,281 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 287 points
2025-07-27 00:03:07,281 - [BITVAVO] - root - INFO - DOT/USDT B&H total return: -14.88%
2025-07-27 00:03:07,284 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-27 00:03:07,297 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 00:03:07,414 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-27 00:03:07,426 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 00:03:09,683 - [BITVAVO] - root - INFO - Added ETH/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,683 - [BITVAVO] - root - INFO - Added BTC/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,683 - [BITVAVO] - root - INFO - Added SOL/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,683 - [BITVAVO] - root - INFO - Added SUI/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,683 - [BITVAVO] - root - INFO - Added XRP/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,683 - [BITVAVO] - root - INFO - Added AAVE/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,683 - [BITVAVO] - root - INFO - Added AVAX/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,683 - [BITVAVO] - root - INFO - Added ADA/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,683 - [BITVAVO] - root - INFO - Added LINK/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO - Added TRX/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO - Added PEPE/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO - Added DOGE/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO - Added BNB/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO - Added DOT/USDT buy-and-hold curve with 287 points
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO -   - ETH/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO -   - BTC/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO -   - SOL/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO -   - SUI/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,684 - [BITVAVO] - root - INFO -   - XRP/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,685 - [BITVAVO] - root - INFO -   - AAVE/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,685 - [BITVAVO] - root - INFO -   - AVAX/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,685 - [BITVAVO] - root - INFO -   - ADA/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,685 - [BITVAVO] - root - INFO -   - LINK/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,685 - [BITVAVO] - root - INFO -   - TRX/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,685 - [BITVAVO] - root - INFO -   - PEPE/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,685 - [BITVAVO] - root - INFO -   - DOGE/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,685 - [BITVAVO] - root - INFO -   - BNB/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,685 - [BITVAVO] - root - INFO -   - DOT/USDT: 287 points from 2024-10-13 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,715 - [BITVAVO] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-27 00:03:09,715 - [BITVAVO] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-27 00:03:09,718 - [BITVAVO] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-27 00:03:09,718 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-27 00:03:09,730 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-27 00:03:09,730 - [BITVAVO] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-27 00:03:09,730 - [BITVAVO] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-27 00:03:09,730 - [BITVAVO] - root - INFO - Combination method: consensus
2025-07-27 00:03:09,730 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-27 00:03:09,730 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-27 00:03:09,747 - [BITVAVO] - root - INFO - Loaded 2168 rows of BTC/USDT data from cache (last updated: 2025-07-27)
2025-07-27 00:03:09,748 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-27
2025-07-27 00:03:09,748 - [BITVAVO] - root - INFO - Loaded 2168 rows of BTC/USDT data from cache (after filtering).
2025-07-27 00:03:09,748 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-27 00:03:09,749 - [BITVAVO] - root - INFO - Fetched BTC data: 2168 candles from 2019-08-20 00:00:00+00:00 to 2025-07-26 00:00:00+00:00
2025-07-27 00:03:09,749 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-27 00:03:10,121 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1138)}
2025-07-27 00:03:10,121 - [BITVAVO] - root - INFO - PGO signal: 1
2025-07-27 00:03:10,121 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-27 00:03:10,236 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1142)}
2025-07-27 00:03:10,236 - [BITVAVO] - root - INFO - Bollinger Bands signal: 1
2025-07-27 00:03:12,565 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:03:15,615 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-27 00:03:15,615 - [BITVAVO] - root - INFO - DWMA Score signal: 1
2025-07-27 00:03:16,201 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-27 00:03:16,201 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(899)}
2025-07-27 00:03:16,202 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-27 00:03:16,202 - [BITVAVO] - root - INFO - DEMA Super Score signal: 1
2025-07-27 00:03:17,804 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-27 00:03:17,804 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(994)}
2025-07-27 00:03:17,804 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-27 00:03:17,805 - [BITVAVO] - root - INFO - DPSD Score signal: 1
2025-07-27 00:03:17,903 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-27 00:03:17,903 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-27 00:03:17,903 - [BITVAVO] - root - INFO - AAD Score signal: 1
2025-07-27 00:03:18,694 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-27 00:03:18,694 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-27 00:03:18,694 - [BITVAVO] - root - INFO - Dynamic EMA Score signal: 1
2025-07-27 00:03:20,239 - [BITVAVO] - root - INFO - Quantile DEMA Score signal: 1
2025-07-27 00:03:20,240 - [BITVAVO] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-27 00:03:20,240 - [BITVAVO] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-27 00:03:20,240 - [BITVAVO] - root - INFO - MTPI Score: 1.000000
2025-07-27 00:03:20,240 - [BITVAVO] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-27 00:03:20,243 - [BITVAVO] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-27 00:03:20,244 - [BITVAVO] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 12.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 9.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 11.0, 'BNB/USDT': 4.0, 'DOT/USDT': 3.0}
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 13.0, 'BTC/USDT': 1.0, 'SOL/USDT': 5.0, 'SUI/USDT': 12.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 9.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 11.0, 'BNB/USDT': 4.0, 'DOT/USDT': 3.0}
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 13.0)
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 1.0)
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 5.0)
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 12.0)
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 0.0)
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 7.0)
2025-07-27 00:03:20,244 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 9.0)
2025-07-27 00:03:20,245 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 8.0)
2025-07-27 00:03:20,245 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 2.0)
2025-07-27 00:03:20,245 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 4.0)
2025-07-27 00:03:20,245 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 11.0)
2025-07-27 00:03:20,245 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 4.0)
2025-07-27 00:03:20,245 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 3.0)
2025-07-27 00:03:20,245 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 00:03:20,245 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 00:03:20,245 - [BITVAVO] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 00:03:20,261 - [BITVAVO] - root - INFO - Appended metrics to existing file: Performance_Metrics/metrics_WeightedAllocation_1d_1d_weighted_50-50_assets14_since_20250720_run_20250724_215901.csv
2025-07-27 00:03:20,261 - [BITVAVO] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_WeightedAllocation_1d_1d_weighted_50-50_assets14_since_20250720_run_20250724_215901.csv
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO - Results type: <class 'dict'>
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO - Success flag set to: True
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 287 entries
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 287 entries
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 287 entries
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 287 entries
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-27 00:03:20,262 - [BITVAVO] - root - INFO -   - metrics_file: <class 'str'>
2025-07-27 00:03:20,263 - [BITVAVO] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-27 00:03:20,263 - [BITVAVO] - root - INFO -   - success: <class 'bool'>
2025-07-27 00:03:20,263 - [BITVAVO] - root - INFO -   - message: <class 'str'>
2025-07-27 00:03:20,263 - [BITVAVO] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-27 00:03:20,263 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: ETH/EUR, DOGE/EUR
2025-07-27 00:03:20,264 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-22 00:00:00+00:00    XRP/EUR, DOGE/EUR
2025-07-23 00:00:00+00:00    XRP/EUR, DOGE/EUR
2025-07-24 00:00:00+00:00    ETH/EUR, DOGE/EUR
2025-07-25 00:00:00+00:00    ETH/EUR, DOGE/EUR
2025-07-26 00:00:00+00:00    ETH/EUR, DOGE/EUR
dtype: object
2025-07-27 00:03:20,264 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 00:03:20,264 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 00:03:20,264 - [BITVAVO] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-07-27 00:03:20,264 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-27 00:03:20,265 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 00:03:20,265 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 00:03:20,265 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-07-27 00:03:20,265 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['ETH/EUR']
2025-07-27 00:03:20,265 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: ETH/EUR
2025-07-27 00:03:20,265 - [BITVAVO] - root - ERROR - [DEBUG] SELECTED BEST ASSET: ETH/EUR (score: 13.0)
2025-07-27 00:03:20,265 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: ETH/EUR (MTPI signal: 1)
2025-07-27 00:03:20,265 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-07-27 00:03:20,265 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['ETH/EUR']
2025-07-27 00:03:20,265 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: ETH/EUR, DOGE/EUR -> ETH/EUR
2025-07-27 00:03:20,265 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - Single winner: ETH/EUR
2025-07-27 00:03:20,293 - [BITVAVO] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-27 00:03:20,293 - [BITVAVO] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 00:03:20,293 - [BITVAVO] - root - INFO - Available assets from config: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 00:03:20,293 - [BITVAVO] - root - INFO - Asset columns found in dataframe: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-27 00:03:20,293 - [BITVAVO] - root - INFO - Assets with non-zero allocation: ['ETH/EUR', 'DOGE/EUR']
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - All assets sorted by score: [('ETH/EUR', 13.0), ('SUI/EUR', 12.0), ('DOGE/EUR', 11.0), ('XRP/EUR', 9.0), ('ADA/EUR', 9.0), ('LINK/EUR', 8.0), ('AVAX/EUR', 7.0), ('SOL/EUR', 5.0), ('PEPE/EUR', 4.0), ('BNB/EUR', 4.0), ('DOT/EUR', 3.0), ('TRX/EUR', 2.0), ('BTC/EUR', 1.0), ('AAVE/EUR', 0.0)]
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - Selected top 2 assets by score: ['ETH/EUR', 'SUI/EUR']
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - Updated assets list with top-scoring assets: ['ETH/EUR', 'SUI/EUR']
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - Using weighted allocation with configured weights: {'ETH/EUR': 0.5, 'SUI/EUR': 0.5}
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - [DEBUG]   - Best asset selected: ETH/EUR
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - [DEBUG]   - Assets held: {'ETH/EUR': 0.5, 'SUI/EUR': 0.5}
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-27 00:03:20,294 - [BITVAVO] - root - ERROR - 🚨 ? ETH/EUR WAS SELECTED
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - Executing multi-asset strategy with 2 assets: {'ETH/EUR': 0.5, 'SUI/EUR': 0.5}
2025-07-27 00:03:20,294 - [BITVAVO] - root - INFO - Passing 14 asset scores to executor for replacement logic
2025-07-27 00:03:20,365 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-27 00:03:20,366 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: bitvavo
2025-07-27 00:03:20,366 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-27 00:03:20,366 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-27 00:03:20,366 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-27 00:03:20,436 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-27 00:03:20,436 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': 1753574596045, 'datetime': '2025-07-27T00:03:16.045Z', 'high': 3230.1, 'low': 3147.0, 'bid': 3182.2, 'bidVolume': 0.64218902, 'ask': 3182.8, 'askVolume': 3.13907679, 'vwap': 3189.1719623822264, 'open': 3166.6, 'close': 3183.1, 'last': 3183.1, 'previousClose': None, 'change': 16.5, 'percentage': 0.5210636013389754, 'average': 3174.85, 'baseVolume': 7300.81941499, 'quoteVolume': 23283568.580701914, 'info': {'market': 'ETH-EUR', 'startTimestamp': '1753488196045', 'timestamp': '1753574596045', 'open': '3166.6', 'openTimestamp': '1753488217801', 'high': '3230.1', 'low': '3147', 'last': '3183.1', 'closeTimestamp': '1753574552138', 'bid': '3182.200', 'bidSize': '0.64218902', 'ask': '3182.800', 'askSize': '3.13907679', 'volume': '7300.81941499', 'volumeQuote': '23283568.580701915'}, 'indexPrice': None, 'markPrice': None}
2025-07-27 00:03:20,436 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3183.1
2025-07-27 00:03:20,437 - [BITVAVO] - root - WARNING - Adjusted amount 0.00003438 is below minimum order size 0.0001 for ETH/EUR
2025-07-27 00:03:20,437 - [BITVAVO] - root - INFO - Using minimum order size 0.0001 for ETH/EUR
2025-07-27 00:03:20,437 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-27 00:03:20,437 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-27 00:03:20,437 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Using existing exchange instance
2025-07-27 00:03:20,438 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found in exchange markets
2025-07-27 00:03:20,438 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-27 00:03:20,496 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-27 00:03:20,496 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1753574595744, 'datetime': '2025-07-27T00:03:15.744Z', 'high': 3.695, 'low': 3.3536, 'bid': 3.5353, 'bidVolume': 245.54303403, 'ask': 3.5368, 'askVolume': 325.28183679, 'vwap': 3.542380552620114, 'open': 3.3591, 'close': 3.5406, 'last': 3.5406, 'previousClose': None, 'change': 0.1815, 'percentage': 5.403233008841654, 'average': 3.44985, 'baseVolume': 2235951.29627738, 'quoteVolume': 7920590.388538726, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1753488195744', 'timestamp': '1753574595744', 'open': '3.3591', 'openTimestamp': '1753488217057', 'high': '3.695', 'low': '3.3536', 'last': '3.5406', 'closeTimestamp': '1753574577395', 'bid': '3.535300', 'bidSize': '245.54303403', 'ask': '3.536800', 'askSize': '325.28183679', 'volume': '2235951.29627738', 'volumeQuote': '7920590.388538725671'}, 'indexPrice': None, 'markPrice': None}
2025-07-27 00:03:20,496 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 3.5406
2025-07-27 00:03:20,497 - [BITVAVO] - root - WARNING - Order value 0.10945000 is below minimum order value 5.0 for SUI/EUR
2025-07-27 00:03:20,497 - [BITVAVO] - root - INFO - Using minimum amount for cost 1.41219002 for SUI/EUR
2025-07-27 00:03:20,497 - [BITVAVO] - root - INFO - Original assets with weights: {'ETH/EUR': 0.5, 'SUI/EUR': 0.5}
2025-07-27 00:03:20,825 - [BITVAVO] - root - INFO - Using provided asset scores for replacement logic: {'ETH/EUR': 13.0, 'BTC/EUR': 1.0, 'SOL/EUR': 5.0, 'SUI/EUR': 12.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 0.0, 'AVAX/EUR': 7.0, 'ADA/EUR': 9.0, 'LINK/EUR': 8.0, 'TRX/EUR': 2.0, 'PEPE/EUR': 4.0, 'DOGE/EUR': 11.0, 'BNB/EUR': 4.0, 'DOT/EUR': 3.0}
2025-07-27 00:03:20,825 - [BITVAVO] - root - INFO - Top assets by score:
2025-07-27 00:03:20,825 - [BITVAVO] - root - INFO -   ETH/EUR: score=13.0, daily trade limit: OK
2025-07-27 00:03:20,825 - [BITVAVO] - root - INFO -   SUI/EUR: score=12.0, daily trade limit: OK
2025-07-27 00:03:20,826 - [BITVAVO] - root - INFO -   DOGE/EUR: score=11.0, daily trade limit: OK
2025-07-27 00:03:20,826 - [BITVAVO] - root - INFO -   XRP/EUR: score=9.0, daily trade limit: OK
2025-07-27 00:03:20,826 - [BITVAVO] - root - INFO -   ADA/EUR: score=9.0, daily trade limit: OK
2025-07-27 00:03:20,826 - [BITVAVO] - root - INFO -   LINK/EUR: score=8.0, daily trade limit: OK
2025-07-27 00:03:20,826 - [BITVAVO] - root - INFO -   AVAX/EUR: score=7.0, daily trade limit: OK
2025-07-27 00:03:20,826 - [BITVAVO] - root - INFO -   SOL/EUR: score=5.0, daily trade limit: OK
2025-07-27 00:03:20,826 - [BITVAVO] - root - INFO -   PEPE/EUR: score=4.0, daily trade limit: OK
2025-07-27 00:03:20,827 - [BITVAVO] - root - INFO -   BNB/EUR: score=4.0, daily trade limit: OK
2025-07-27 00:03:20,827 - [BITVAVO] - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-27 00:03:20,827 - [BITVAVO] - root - INFO - No assets were rejected during execution
2025-07-27 00:03:20,827 - [BITVAVO] - root - ERROR - Error executing strategy: 'TradingExecutor' object has no attribute 'get_trade_logger'
Traceback (most recent call last):
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/background_service.py", line 1802, in execute_strategy
    trade_result = self.trading_executor.execute_multi_asset_strategy(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/src/trading/executor.py", line 543, in execute_multi_asset_strategy
    exit_result = self.exit_position(asset)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/src/trading/executor.py", line 1240, in exit_position
    trade_logger = self.get_trade_logger()
                   ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradingExecutor' object has no attribute 'get_trade_logger'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/background_service.py", line 1802, in execute_strategy
    trade_result = self.trading_executor.execute_multi_asset_strategy(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/src/trading/executor.py", line 543, in execute_multi_asset_strategy
    exit_result = self.exit_position(asset)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/src/trading/executor.py", line 1240, in exit_position
    trade_logger = self.get_trade_logger()
                   ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'TradingExecutor' object has no attribute 'get_trade_logger'
2025-07-27 00:03:20,852 - [BITVAVO] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-27 00:03:20,896 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 400 Bad Request"
2025-07-27 00:03:20,898 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Can't parse entities: can't find end of the entity starting at byte offset 121
2025-07-27 00:03:20,930 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-27 00:03:20,934 - [BITVAVO] - root - WARNING - Recovery from strategy_execution_failure failure was unsuccessful
2025-07-27 00:03:20,949 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 400 Bad Request"
2025-07-27 00:03:20,950 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Can't parse entities: can't find end of the entity starting at byte offset 121
2025-07-27 00:03:20,984 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-27 00:03:22,866 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:03:33,130 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:03:43,402 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:03:53,670 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:04:03,932 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:04:14,201 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:04:24,464 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:04:34,730 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:04:44,996 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:04:55,266 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:05:05,529 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:05:15,794 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:05:26,060 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:05:36,325 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:05:46,583 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:05:56,847 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:06:07,115 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:06:17,380 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:06:20,987 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:06:27,647 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:06:37,914 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:06:48,182 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:06:58,446 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:07:08,711 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:07:18,981 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:07:29,251 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:07:39,515 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:07:49,776 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:08:00,488 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:08:10,752 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:08:21,015 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:08:31,274 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:08:41,539 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:08:51,803 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:09:02,070 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:09:12,334 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:09:22,600 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:09:32,867 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:09:43,132 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 00:09:53,400 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-27 12:00:22,322 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Starting get_current_price
2025-07-27 12:00:22,323 - [BITVAVO] - root - INFO - [DEBUG] PRICE - ETH/EUR: Exchange ID: bitvavo
2025-07-27 12:00:22,323 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Using existing exchange instance
2025-07-27 12:00:22,323 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Symbol found in exchange markets
2025-07-27 12:00:22,323 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Attempting to fetch ticker...
2025-07-27 12:00:22,434 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker fetched successfully
2025-07-27 12:00:22,434 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Ticker data: {'symbol': 'ETH/EUR', 'timestamp': 1753617616072, 'datetime': '2025-07-27T12:00:16.072Z', 'high': 3269.5, 'low': 3160.0, 'bid': 3249.1, 'bidVolume': 1.07947972, 'ask': 3249.5, 'askVolume': 1.492122, 'vwap': 3210.99347628692, 'open': 3186.5, 'close': 3249.0, 'last': 3249.0, 'previousClose': None, 'change': 62.5, 'percentage': 1.9613996547936607, 'average': 3217.75, 'baseVolume': 7479.39070812, 'quoteVolume': 24016274.770374328, 'info': {'market': 'ETH-EUR', 'startTimestamp': '1753531216072', 'timestamp': '1753617616072', 'open': '3186.5', 'openTimestamp': '1753531240311', 'high': '3269.5', 'low': '3.16E+3', 'last': '3249', 'closeTimestamp': '1753617606245', 'bid': '3249.100', 'bidSize': '1.07947972', 'ask': '3249.500', 'askSize': '1.49212200', 'volume': '7479.39070812', 'volumeQuote': '24016274.770374327'}, 'indexPrice': None, 'markPrice': None}
2025-07-27 12:00:22,434 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - ETH/EUR: Last price: 3249.0
2025-07-27 12:00:22,434 - [BITVAVO] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Starting get_current_price
2025-07-27 12:00:22,434 - [BITVAVO] - root - INFO - [DEBUG] PRICE - DOGE/EUR: Exchange ID: bitvavo
2025-07-27 12:00:22,434 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Using existing exchange instance
2025-07-27 12:00:22,434 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Symbol found in exchange markets
2025-07-27 12:00:22,434 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Attempting to fetch ticker...
2025-07-27 12:00:22,496 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker fetched successfully
2025-07-27 12:00:22,497 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Ticker data: {'symbol': 'DOGE/EUR', 'timestamp': 1753617616072, 'datetime': '2025-07-27T12:00:16.072Z', 'high': 0.20705, 'low': 0.19955, 'bid': 0.20132, 'bidVolume': 5902.17174858, 'ask': 0.20133, 'askVolume': 3066.91016, 'vwap': 0.20248581204109797, 'open': 0.20175, 'close': 0.20142, 'last': 0.20142, 'previousClose': None, 'change': -0.00033, 'percentage': -0.1635687732342007, 'average': 0.201585, 'baseVolume': 9393316.31870819, 'quoteVolume': 1902013.2825525247, 'info': {'market': 'DOGE-EUR', 'startTimestamp': '1753531216072', 'timestamp': '1753617616072', 'open': '0.20175', 'openTimestamp': '1753531308311', 'high': '0.20705', 'low': '0.19955', 'last': '0.20142', 'closeTimestamp': '1753617606879', 'bid': '0.2013200', 'bidSize': '5902.17174858', 'ask': '0.2013300', 'askSize': '3066.91016000', 'volume': '9393316.31870819', 'volumeQuote': '1902013.2825525248002'}, 'indexPrice': None, 'markPrice': None}
2025-07-27 12:00:22,497 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - DOGE/EUR: Last price: 0.20142
2025-07-27 12:00:22,588 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-27 12:00:22,589 - [BITVAVO] - root - INFO - Status update sent
