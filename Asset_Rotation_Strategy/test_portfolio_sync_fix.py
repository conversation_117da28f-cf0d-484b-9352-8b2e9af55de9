#!/usr/bin/env python3
"""
Focused test for the portfolio sync fix.
Tests the specific scenario where an asset was sold but not removed from portfolio tracking.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.trading.executor import TradingExecutor
from src.trading.paper_trading import PaperTradingExecutor
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_portfolio_sync_fix():
    """Test the portfolio sync fix for detecting and correcting tracking discrepancies."""
    
    print("=" * 80)
    print("TESTING PORTFOLIO SYNC FIX")
    print("=" * 80)
    
    # Initialize clean paper trading
    paper_trading = PaperTradingExecutor(initial_balance=5000.0, quote_currency='EUR')
    # Clear any existing positions
    paper_trading.positions = {}
    
    # Create executor
    executor = TradingExecutor('kraken', test_mode=True)
    executor.paper_trading = paper_trading
    executor.trading_config = {'mode': 'paper', 'enabled': True}
    
    print("\n1. SETUP INITIAL STATE:")
    
    # Set up initial portfolio tracking (simulating previous state)
    executor.current_portfolio = {'XRP/EUR': 0.5, 'DOGE/EUR': 0.5}
    
    # Add only DOGE position to paper trading (simulating XRP was sold but not tracked)
    paper_trading.positions['DOGE/EUR'] = {
        'amount': 2450.0,
        'entry_price': 0.20,
        'entry_time': '2025-07-23T00:00:00',
        'cost': 490.0,
        'current_price': 0.204,
        'current_value': 499.8,
        'pnl': 9.8,
        'pnl_pct': 2.0
    }
    
    # Reduce balance to simulate money was used for positions
    paper_trading.balances['EUR'] = 4500.0  # Simulating XRP proceeds already added
    
    print(f"   Portfolio tracking: {executor.current_portfolio}")
    print(f"   Actual positions: {list(paper_trading.get_positions().keys())}")
    print(f"   Available EUR: {paper_trading.get_balance('EUR')['EUR']:.2f}")
    print("   DISCREPANCY: XRP/EUR is tracked but not in actual positions!")
    
    print("\n2. TESTING PORTFOLIO SYNC:")
    
    # Mock price function
    def mock_get_current_price(symbol):
        prices = {
            'XRP/EUR': 2.69953,
            'DOGE/EUR': 0.204,
            'ETH/EUR': 3078.90
        }
        return prices.get(symbol, 0.0)
    
    executor.get_current_price = mock_get_current_price
    
    # Call the sync method directly
    print("   Calling _sync_portfolio_state()...")
    executor._sync_portfolio_state()
    
    print(f"   Portfolio tracking after sync: {executor.current_portfolio}")
    print(f"   Actual positions: {list(paper_trading.get_positions().keys())}")
    
    print("\n3. VERIFICATION:")
    
    # Check if XRP was removed from tracking
    if 'XRP/EUR' not in executor.current_portfolio:
        print("   ✅ SUCCESS: XRP/EUR was correctly removed from portfolio tracking")
    else:
        print("   ❌ FAILURE: XRP/EUR is still in portfolio tracking")
    
    # Check if DOGE is still tracked
    if 'DOGE/EUR' in executor.current_portfolio:
        print("   ✅ SUCCESS: DOGE/EUR is still correctly tracked")
    else:
        print("   ❌ FAILURE: DOGE/EUR was incorrectly removed from tracking")
    
    # Check total weight (sync doesn't normalize, that's correct behavior)
    total_weight = sum(executor.current_portfolio.values())
    print(f"   Total portfolio weight: {total_weight:.4f}")
    print("   ℹ️  NOTE: Sync method correctly removes assets but doesn't normalize weights")
    print("   ℹ️  Weight normalization happens during strategy execution")
    
    print("\n4. TESTING WITH MULTIPLE DISCREPANCIES:")
    
    # Add another tracked asset that doesn't exist
    executor.current_portfolio = {'XRP/EUR': 0.3, 'DOGE/EUR': 0.4, 'ETH/EUR': 0.3}
    
    print(f"   Portfolio tracking before sync: {executor.current_portfolio}")
    print(f"   Actual positions: {list(paper_trading.get_positions().keys())}")
    
    executor._sync_portfolio_state()
    
    print(f"   Portfolio tracking after sync: {executor.current_portfolio}")
    
    # Verify only DOGE remains (weight normalization happens during strategy execution)
    if len(executor.current_portfolio) == 1 and 'DOGE/EUR' in executor.current_portfolio:
        print("   ✅ SUCCESS: Multiple discrepancies corrected, only DOGE remains")
        print(f"   ℹ️  DOGE weight after sync: {executor.current_portfolio['DOGE/EUR']}")
    else:
        print("   ❌ FAILURE: Multiple discrepancies not properly corrected")
    
    print("\n" + "=" * 80)
    print("PORTFOLIO SYNC TEST COMPLETED")
    print("=" * 80)
    
    return True

if __name__ == "__main__":
    test_portfolio_sync_fix()
